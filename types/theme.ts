export type ThemeMode = 'light' | 'dark';

export interface ColorScheme {
  // Background colors
  background: {
    primary: string;
    secondary: string;
    tertiary: string;
    card: string;
    modal: string;
  };
  
  // Text colors
  text: {
    primary: string;
    secondary: string;
    disabled: string;
    inverse: string;
  };
  
  // Accent colors
  accent: {
    primary: string;
    secondary: string;
    hover: string;
  };
  
  // Status colors
  status: {
    success: string;
    warning: string;
    error: string;
    info: string;
  };
  
  // Interactive elements
  interactive: {
    button: {
      background: string;
      text: string;
      hover: string;
      disabled: string;
      pressed: string;
    };
    input: {
      background: string;
      border: string;
      focusBorder: string;
      placeholder: string;
      errorBorder: string;
    };
    link: {
      default: string;
      hover: string;
      pressed: string;
    };
    card: {
      background: string;
      border: string;
      hover: string;
      pressed: string;
    };
  };
  
  // UI elements
  ui: {
    border: string;
    shadow: string;
    overlay: string;
    tabBar: {
      background: string;
      border: string;
      active: string;
      inactive: string;
    };
  };
  
  // Gradients
  gradients: {
    primary: [string, string];
    secondary: [string, string];
    accent: [string, string];
  };
}

export interface Theme {
  mode: ThemeMode;
  colors: ColorScheme;
  spacing: {
    xs: number;
    sm: number;
    md: number;
    lg: number;
    xl: number;
    xxl: number;
  };
  borderRadius: {
    sm: number;
    md: number;
    lg: number;
    xl: number;
    full: number;
  };
  shadows: {
    sm: {
      shadowColor: string;
      shadowOffset: { width: number; height: number };
      shadowOpacity: number;
      shadowRadius: number;
      elevation: number;
    };
    md: {
      shadowColor: string;
      shadowOffset: { width: number; height: number };
      shadowOpacity: number;
      shadowRadius: number;
      elevation: number;
    };
    lg: {
      shadowColor: string;
      shadowOffset: { width: number; height: number };
      shadowOpacity: number;
      shadowRadius: number;
      elevation: number;
    };
  };
  transitions: {
    fast: number;
    normal: number;
    slow: number;
  };
}

export interface ThemeContextType {
  theme: Theme;
  themeMode: ThemeMode;
  toggleTheme: () => void;
  setTheme: (mode: ThemeMode) => void;
}
