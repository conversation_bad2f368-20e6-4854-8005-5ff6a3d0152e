import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  ScrollView,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Plus, X, Palette, CreditCard as Edit3, Trash2 } from 'lucide-react-native';
import { Subject } from '@/types/app';
import { useSubjects } from '@/hooks/useSubjects';
import { useTheme } from '@/contexts/ThemeContext';

interface SubjectPickerProps {
  selectedSubject: Subject | null;
  onSelectSubject: (subject: Subject | null) => void;
}

const PRESET_COLORS = [
  '#6366F1', '#8B5CF6', '#EC4899', '#EF4444', '#F59E0B',
  '#10B981', '#06B6D4', '#3B82F6', '#84CC16', '#F97316',
  '#A855F7', '#14B8A6', '#F472B6', '#FB7185', '#FBBF24',
];

export default function SubjectPicker({ selectedSubject, onSelectSubject }: SubjectPickerProps) {
  const { theme } = useTheme();
  const { subjects, addSubject, updateSubject, deleteSubject } = useSubjects();
  const [showModal, setShowModal] = useState(false);
  const [editingSubject, setEditingSubject] = useState<Subject | null>(null);
  const [subjectName, setSubjectName] = useState('');
  const [selectedColor, setSelectedColor] = useState(PRESET_COLORS[0]);

  const openAddModal = () => {
    setEditingSubject(null);
    setSubjectName('');
    setSelectedColor(PRESET_COLORS[0]);
    setShowModal(true);
  };

  const openEditModal = (subject: Subject) => {
    setEditingSubject(subject);
    setSubjectName(subject.name);
    setSelectedColor(subject.color);
    setShowModal(true);
  };

  const handleSave = () => {
    if (!subjectName.trim()) {
      Alert.alert('Error', 'Please enter a subject name');
      return;
    }

    if (editingSubject) {
      updateSubject(editingSubject.id, {
        name: subjectName.trim(),
        color: selectedColor,
      });
    } else {
      addSubject({
        name: subjectName.trim(),
        color: selectedColor,
      });
    }

    setShowModal(false);
  };

  const handleDelete = (subject: Subject) => {
    Alert.alert(
      'Delete Subject',
      `Are you sure you want to delete "${subject.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            const wasSelected = selectedSubject?.id === subject.id;
            deleteSubject(subject.id);
            if (wasSelected) {
              // Find a new subject to select
              const remainingSubjects = subjects.filter(s => s.id !== subject.id);
              onSelectSubject(remainingSubjects.length > 0 ? remainingSubjects[0] : null);
            }
          },
        },
      ]
    );
  };

  return (
    <>
      <View style={styles.container}>
        <Text style={[styles.label, { color: theme.colors.text.primary }]}>Subject</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.subjectList}>
          {subjects.map((subject) => (
            <TouchableOpacity
              key={subject.id}
              style={[
                styles.subjectChip,
                { backgroundColor: theme.colors.background.secondary, borderColor: theme.colors.ui.border },
                selectedSubject?.id === subject.id && {
                  backgroundColor: theme.colors.background.tertiary,
                  borderColor: subject.color,
                },
              ]}
              onPress={() => onSelectSubject(subject)}
              onLongPress={() => openEditModal(subject)}
            >
              <View style={[styles.colorDot, { backgroundColor: subject.color }]} />
              <Text
                style={[
                  styles.subjectText,
                  { color: theme.colors.text.secondary },
                  selectedSubject?.id === subject.id && { color: theme.colors.accent.primary },
                ]}
              >
                {subject.name}
              </Text>
            </TouchableOpacity>
          ))}
          
          <TouchableOpacity
            style={[
              styles.addButton,
              { backgroundColor: theme.colors.background.secondary, borderColor: theme.colors.ui.border },
            ]}
            onPress={openAddModal}
          >
            <Plus size={16} color={theme.colors.accent.primary} />
          </TouchableOpacity>
        </ScrollView>
      </View>

      <Modal visible={showModal} transparent animationType="slide">
        <View style={[styles.modalOverlay, { backgroundColor: theme.colors.ui.overlay }]}>
          <View style={[styles.modalContent, { backgroundColor: theme.colors.background.modal, ...theme.shadows.lg }]}>
            <View style={[styles.modalHeader, { borderBottomColor: theme.colors.ui.border }]}>
              <Text style={[styles.modalTitle, { color: theme.colors.text.primary }]}>
                {editingSubject ? 'Edit Subject' : 'Add Subject'}
              </Text>
              <TouchableOpacity onPress={() => setShowModal(false)}>
                <X size={24} color={theme.colors.text.secondary} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalBody}>
              <View style={styles.inputGroup}>
                <Text style={[styles.inputLabel, { color: theme.colors.text.primary }]}>Subject Name</Text>
                <TextInput
                  style={[
                    styles.textInput,
                    {
                      backgroundColor: theme.colors.interactive.input.background,
                      borderColor: theme.colors.interactive.input.border,
                      color: theme.colors.text.primary,
                    },
                  ]}
                  value={subjectName}
                  onChangeText={setSubjectName}
                  placeholder="Enter subject name"
                  placeholderTextColor={theme.colors.interactive.input.placeholder}
                />
              </View>

              <View style={styles.inputGroup}>
                <Text style={[styles.inputLabel, { color: theme.colors.text.primary }]}>Color</Text>
                <View style={styles.colorGrid}>
                  {PRESET_COLORS.map((color) => (
                    <TouchableOpacity
                      key={color}
                      style={[
                        styles.colorOption,
                        { backgroundColor: color },
                        selectedColor === color && {
                          borderColor: theme.colors.text.inverse,
                          shadowColor: theme.shadows.md.shadowColor,
                          shadowOffset: theme.shadows.md.shadowOffset,
                          shadowOpacity: theme.shadows.md.shadowOpacity,
                          shadowRadius: theme.shadows.md.shadowRadius,
                          elevation: theme.shadows.md.elevation,
                        },
                      ]}
                      onPress={() => setSelectedColor(color)}
                    />
                  ))}
                </View>
              </View>

              <View style={styles.modalActions}>
                {editingSubject && (
                  <TouchableOpacity
                    style={[
                      styles.deleteButton,
                      { backgroundColor: theme.colors.status.error + '1A', borderColor: theme.colors.status.error },
                    ]}
                    onPress={() => {
                      setShowModal(false);
                      handleDelete(editingSubject);
                    }}
                  >
                    <Trash2 size={16} color={theme.colors.status.error} />
                    <Text style={[styles.deleteText, { color: theme.colors.status.error }]}>Delete</Text>
                  </TouchableOpacity>
                )}
                
                <View style={styles.actionButtons}>
                  <TouchableOpacity
                    style={[styles.cancelButton, { backgroundColor: theme.colors.background.tertiary }]}
                    onPress={() => setShowModal(false)}
                  >
                    <Text style={[styles.cancelText, { color: theme.colors.text.secondary }]}>Cancel</Text>
                  </TouchableOpacity>
                  
                  <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
                    <LinearGradient
                      colors={[theme.colors.accent.primary, theme.colors.accent.secondary]}
                      style={styles.saveGradient}
                    >
                      <Text style={[styles.saveText, { color: theme.colors.text.inverse }]}>
                        {editingSubject ? 'Update' : 'Add'}
                      </Text>
                    </LinearGradient>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 12,
  },
  subjectList: {
    flexDirection: 'row',
  },
  subjectChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 2,
    marginRight: 8,
    gap: 6,
  },
  subjectChipSelected: {
    // Handled inline
  },
  colorDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  subjectText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  subjectTextSelected: {
    // Handled inline
  },
  addButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    borderWidth: 2,
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    borderRadius: 20,
    width: '100%',
    maxWidth: 400,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
  },
  modalBody: {
    padding: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
  },
  colorGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  colorOption: {
    width: 40,
    height: 40,
    borderRadius: 20,
    borderWidth: 3,
    borderColor: 'transparent',
  },
  colorOptionSelected: {
    // Handled inline
  },
  modalActions: {
    gap: 16,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
  },
  deleteText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: 'center',
  },
  cancelText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
  saveButton: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  saveGradient: {
    paddingVertical: 14,
    alignItems: 'center',
  },
  saveText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
  },
});
