import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { useTheme } from '@/contexts/ThemeContext';

const AnimatedTouchableOpacity = Animated.createAnimatedComponent(TouchableOpacity);

export type CardVariant = 'default' | 'elevated' | 'outlined' | 'filled';
export type CardSize = 'sm' | 'md' | 'lg';

interface CardProps {
  children: React.ReactNode;
  onPress?: () => void;
  variant?: CardVariant;
  size?: CardSize;
  style?: ViewStyle;
  gradient?: boolean;
  gradientColors?: readonly [string, string];
  disabled?: boolean;
  pressable?: boolean;
}

export const Card: React.FC<CardProps> = ({
  children,
  onPress,
  variant = 'default',
  size = 'md',
  style,
  gradient = false,
  gradientColors,
  disabled = false,
  pressable = true,
}) => {
  const { theme } = useTheme();
  const scale = useSharedValue(1);
  const opacity = useSharedValue(1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const handlePressIn = () => {
    if (!onPress || !pressable) return;
    scale.value = withSpring(0.98, { damping: 15, stiffness: 300 });
    opacity.value = withTiming(0.9, { duration: 100 });
  };

  const handlePressOut = () => {
    if (!onPress || !pressable) return;
    scale.value = withSpring(1, { damping: 15, stiffness: 300 });
    opacity.value = withTiming(1, { duration: 100 });
  };

  const getSizeStyles = (): ViewStyle => {
    const sizeStyles: Record<CardSize, ViewStyle> = {
      sm: {
        padding: theme.spacing.sm,
        borderRadius: theme.borderRadius.sm,
      },
      md: {
        padding: theme.spacing.md,
        borderRadius: theme.borderRadius.md,
      },
      lg: {
        padding: theme.spacing.lg,
        borderRadius: theme.borderRadius.lg,
      },
    };
    return sizeStyles[size];
  };

  const getVariantStyles = (): ViewStyle => {
    const variantStyles: Record<CardVariant, ViewStyle> = {
      default: {
        backgroundColor: theme.colors.background.card,
        ...theme.shadows.sm,
      },
      elevated: {
        backgroundColor: theme.colors.background.card,
        ...theme.shadows.lg,
      },
      outlined: {
        backgroundColor: theme.colors.background.card,
        borderWidth: 1,
        borderColor: theme.colors.ui.border,
      },
      filled: {
        backgroundColor: theme.colors.background.secondary,
      },
    };
    return variantStyles[variant];
  };

  const cardStyles = [
    getSizeStyles(),
    getVariantStyles(),
    style,
  ];

  if (gradient) {
    const colors = gradientColors || theme.colors.gradients.primary;
    
    if (onPress && pressable) {
      return (
        <AnimatedTouchableOpacity
          style={[animatedStyle]}
          onPress={onPress}
          onPressIn={handlePressIn}
          onPressOut={handlePressOut}
          disabled={disabled}
          activeOpacity={0.9}
        >
          <LinearGradient
            colors={colors as readonly [string, string]}
            style={cardStyles}
          >
            {children}
          </LinearGradient>
        </AnimatedTouchableOpacity>
      );
    }

    return (
      <LinearGradient
        colors={colors as readonly [string, string]}
        style={cardStyles}
      >
        {children}
      </LinearGradient>
    );
  }

  if (onPress && pressable) {
    return (
      <AnimatedTouchableOpacity
        style={[animatedStyle, cardStyles]}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled}
        activeOpacity={0.9}
      >
        {children}
      </AnimatedTouchableOpacity>
    );
  }

  return (
    <View style={cardStyles}>
      {children}
    </View>
  );
};

// Specialized card variants
interface StatCardProps {
  title: string;
  value: string | number;
  icon?: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  onPress?: () => void;
  style?: ViewStyle;
}

export const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  trend,
  onPress,
  style,
}) => {
  const { theme } = useTheme();

  return (
    <Card
      variant="elevated"
      onPress={onPress}
      pressable={!!onPress}
      style={[styles.statCard, style]}
    >
      <View style={styles.statHeader}>
        {icon && (
          <View style={[styles.statIcon, { backgroundColor: theme.colors.accent.primary + '20' }]}>
            {icon}
          </View>
        )}
        <View style={styles.statContent}>
          <Text style={[styles.statTitle, { color: theme.colors.text.secondary }]}>
            {title}
          </Text>
          <Text style={[styles.statValue, { color: theme.colors.text.primary }]}>
            {value}
          </Text>
        </View>
      </View>
      {trend && (
        <View style={styles.trendContainer}>
          <Text
            style={[
              styles.trendText,
              {
                color: trend.isPositive
                  ? theme.colors.status.success
                  : theme.colors.status.error,
              },
            ]}
          >
            {trend.isPositive ? '+' : ''}{trend.value}%
          </Text>
        </View>
      )}
    </Card>
  );
};

interface ActionCardProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  onPress: () => void;
  style?: ViewStyle;
  variant?: 'default' | 'primary' | 'success' | 'warning' | 'danger';
}

export const ActionCard: React.FC<ActionCardProps> = ({
  title,
  description,
  icon,
  onPress,
  style,
  variant = 'default',
}) => {
  const { theme } = useTheme();

  const getVariantColor = () => {
    const colors = {
      default: theme.colors.accent.primary,
      primary: theme.colors.accent.primary,
      success: theme.colors.status.success,
      warning: theme.colors.status.warning,
      danger: theme.colors.status.error,
    };
    return colors[variant];
  };

  const variantColor = getVariantColor();

  return (
    <Card
      variant="outlined"
      onPress={onPress}
      style={[
        styles.actionCard,
        { borderColor: variantColor + '30' },
        style,
      ]}
    >
      <View style={styles.actionHeader}>
        {icon && (
          <View style={[styles.actionIcon, { backgroundColor: variantColor + '20' }]}>
            {icon}
          </View>
        )}
        <View style={styles.actionContent}>
          <Text style={[styles.actionTitle, { color: theme.colors.text.primary }]}>
            {title}
          </Text>
          {description && (
            <Text style={[styles.actionDescription, { color: theme.colors.text.secondary }]}>
              {description}
            </Text>
          )}
        </View>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  statCard: {
    minHeight: 80,
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  statContent: {
    flex: 1,
  },
  statTitle: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    marginBottom: 4,
  },
  statValue: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
  },
  trendContainer: {
    marginTop: 8,
    alignSelf: 'flex-start',
  },
  trendText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
  },
  actionCard: {
    minHeight: 60,
  },
  actionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 2,
  },
  actionDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
  },
});
