import React, { useState, useRef } from 'react';
import {
  View,
  TextInput,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TextInputProps,
  TouchableOpacity,
} from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  interpolateColor,
} from 'react-native-reanimated';
import { useTheme } from '@/contexts/ThemeContext';

export type InputVariant = 'default' | 'filled' | 'outline';
export type InputSize = 'sm' | 'md' | 'lg';

interface InputProps extends Omit<TextInputProps, 'style'> {
  label?: string;
  error?: string;
  hint?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onRightIconPress?: () => void;
  variant?: InputVariant;
  size?: InputSize;
  style?: ViewStyle;
  inputStyle?: TextStyle;
  required?: boolean;
}

export const Input: React.FC<InputProps> = ({
  label,
  error,
  hint,
  leftIcon,
  rightIcon,
  onRightIconPress,
  variant = 'default',
  size = 'md',
  style,
  inputStyle,
  required = false,
  ...textInputProps
}) => {
  const { theme } = useTheme();
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<TextInput>(null);
  
  const focusAnimation = useSharedValue(0);
  const borderAnimation = useSharedValue(0);

  const animatedContainerStyle = useAnimatedStyle(() => {
    const borderColor = interpolateColor(
      borderAnimation.value,
      [0, 1],
      [theme.colors.interactive.input.border, theme.colors.interactive.input.focusBorder]
    );

    return {
      borderColor,
      borderWidth: withTiming(borderAnimation.value === 1 ? 2 : 1, { duration: 200 }),
    };
  });

  const animatedLabelStyle = useAnimatedStyle(() => {
    if (variant === 'filled') {
      return {
        transform: [
          {
            translateY: withTiming(
              focusAnimation.value === 1 || textInputProps.value ? -8 : 0,
              { duration: 200 }
            ),
          },
          {
            scale: withTiming(
              focusAnimation.value === 1 || textInputProps.value ? 0.85 : 1,
              { duration: 200 }
            ),
          },
        ],
      };
    }
    return {};
  });

  const handleFocus = (e: any) => {
    setIsFocused(true);
    focusAnimation.value = withTiming(1, { duration: 200 });
    borderAnimation.value = withTiming(1, { duration: 200 });
    textInputProps.onFocus?.(e);
  };

  const handleBlur = (e: any) => {
    setIsFocused(false);
    focusAnimation.value = withTiming(0, { duration: 200 });
    borderAnimation.value = withTiming(0, { duration: 200 });
    textInputProps.onBlur?.(e);
  };

  const getSizeStyles = () => {
    const sizeConfig: Record<InputSize, { height: number; fontSize: number; padding: number }> = {
      sm: { height: 36, fontSize: 14, padding: theme.spacing.sm },
      md: { height: 44, fontSize: 16, padding: theme.spacing.md },
      lg: { height: 52, fontSize: 18, padding: theme.spacing.md },
    };
    return sizeConfig[size];
  };

  const getVariantStyles = (): ViewStyle => {
    const { height, padding } = getSizeStyles();
    
    const baseStyles: ViewStyle = {
      borderRadius: theme.borderRadius.md,
      minHeight: height,
      paddingHorizontal: padding,
      flexDirection: 'row',
      alignItems: 'center',
    };

    const variantStyles: Record<InputVariant, ViewStyle> = {
      default: {
        backgroundColor: theme.colors.interactive.input.background,
        borderWidth: 1,
        borderColor: theme.colors.interactive.input.border,
      },
      filled: {
        backgroundColor: theme.colors.interactive.input.background,
        borderWidth: 0,
        borderBottomWidth: 2,
        borderRadius: theme.borderRadius.sm,
        borderBottomColor: theme.colors.interactive.input.border,
      },
      outline: {
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderColor: theme.colors.interactive.input.border,
      },
    };

    return {
      ...baseStyles,
      ...variantStyles[variant],
    };
  };

  const getInputStyles = (): TextStyle => {
    const { fontSize } = getSizeStyles();
    
    return {
      flex: 1,
      fontSize,
      fontFamily: 'Inter-Regular',
      color: theme.colors.text.primary,
      paddingVertical: 0, // Remove default padding
    };
  };

  const getLabelStyles = (): TextStyle => {
    return {
      fontSize: 14,
      fontFamily: 'Inter-Medium',
      color: isFocused ? theme.colors.interactive.input.focusBorder : theme.colors.text.secondary,
      marginBottom: theme.spacing.xs,
    };
  };

  const renderLabel = () => {
    if (!label) return null;

    if (variant === 'filled') {
      return (
        <Animated.Text style={[getLabelStyles(), animatedLabelStyle, styles.floatingLabel]}>
          {label}
          {required && <Text style={{ color: theme.colors.status.error }}> *</Text>}
        </Animated.Text>
      );
    }

    return (
      <Text style={getLabelStyles()}>
        {label}
        {required && <Text style={{ color: theme.colors.status.error }}> *</Text>}
      </Text>
    );
  };

  return (
    <View style={[styles.container, style]}>
      {variant !== 'filled' && renderLabel()}
      
      <Animated.View style={[getVariantStyles(), animatedContainerStyle, error && styles.errorBorder]}>
        {variant === 'filled' && (
          <View style={styles.filledLabelContainer}>
            {renderLabel()}
          </View>
        )}
        
        {leftIcon && (
          <View style={[styles.iconContainer, { marginRight: theme.spacing.xs }]}>
            {leftIcon}
          </View>
        )}
        
        <TextInput
          ref={inputRef}
          style={[getInputStyles(), inputStyle]}
          placeholderTextColor={theme.colors.interactive.input.placeholder}
          onFocus={handleFocus}
          onBlur={handleBlur}
          {...textInputProps}
        />
        
        {rightIcon && (
          <TouchableOpacity
            style={[styles.iconContainer, { marginLeft: theme.spacing.xs }]}
            onPress={onRightIconPress}
            disabled={!onRightIconPress}
          >
            {rightIcon}
          </TouchableOpacity>
        )}
      </Animated.View>
      
      {error && (
        <Text style={[styles.helperText, { color: theme.colors.status.error }]}>
          {error}
        </Text>
      )}
      
      {hint && !error && (
        <Text style={[styles.helperText, { color: theme.colors.text.secondary }]}>
          {hint}
        </Text>
      )}
    </View>
  );
};

// TextArea component for multiline input
interface TextAreaProps extends InputProps {
  rows?: number;
}

export const TextArea: React.FC<TextAreaProps> = ({
  rows = 4,
  style,
  ...props
}) => {
  const { theme } = useTheme();
  
  return (
    <Input
      {...props}
      multiline
      textAlignVertical="top"
      style={[
        {
          minHeight: rows * 20 + theme.spacing.md * 2,
        },
        style,
      ]}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 4,
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  helperText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    marginTop: 4,
    marginLeft: 4,
  },
  errorBorder: {
    borderColor: '#EF4444',
  },
  filledLabelContainer: {
    position: 'absolute',
    top: 0,
    left: 16,
    right: 16,
    zIndex: 1,
  },
  floatingLabel: {
    position: 'absolute',
    left: 0,
    top: 12,
    backgroundColor: 'transparent',
  },
});
