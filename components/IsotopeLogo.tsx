import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useTheme } from '@/contexts/ThemeContext';

interface IsotopeLogoProps {
  size?: 'small' | 'medium' | 'large';
  showText?: boolean;
}

export default function IsotopeLogo({ size = 'medium', showText = true }: IsotopeLogoProps) {
  const { theme } = useTheme();
  const logoSize = size === 'small' ? 24 : size === 'medium' ? 32 : 40;
  const textSize = size === 'small' ? 16 : size === 'medium' ? 20 : 24;

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#6366F1', '#8B5CF6', '#A855F7']}
        style={[styles.logoContainer, { width: logoSize, height: logoSize }]}
      >
        <Text style={[styles.logoText, { fontSize: logoSize * 0.6 }]}>I</Text>
      </LinearGradient>
      {showText && (
        <Text style={[styles.brandText, { fontSize: textSize, color: theme.colors.text.primary }]}>IsotopeAI</Text>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  logoContainer: {
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  logoText: {
    color: '#FFFFFF',
    fontFamily: 'Inter-Bold',
    textAlign: 'center',
  },
  brandText: {
    fontFamily: 'Inter-Bold',
  },
});
