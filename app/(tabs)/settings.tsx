import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Settings as SettingsIcon,
  Timer,
  Bell,
  Palette,
  Info,
  Trash2,
  Download,
  Upload,
  User,
  LogOut,
  Cloud,
  CloudOff,
} from 'lucide-react-native';
import IsotopeLogo from '@/components/IsotopeLogo';
import { useTimer } from '@/hooks/useTimer';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'expo-router';
import { PomodoroSettings } from '@/types/app';
import { useTheme } from '@/contexts/ThemeContext';
import { Switch, Button, Card } from '@/components/ui';

export default function SettingsScreen() {
  const { pomodoroSettings, savePomodoroSettings } = useTimer();
  const { user, signOut } = useAuth();
  const { theme, themeMode, toggleTheme } = useTheme();
  const router = useRouter();
  const [localSettings, setLocalSettings] = useState<PomodoroSettings>(pomodoroSettings);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [signingOut, setSigningOut] = useState(false);

  const handleSaveSettings = () => {
    savePomodoroSettings(localSettings);
    Alert.alert('Success', 'Settings saved successfully!');
  };

  const handleSignOut = async () => {
    setSigningOut(true);
    const { error } = await signOut();
    if (error) {
      Alert.alert('Error', 'Failed to sign out. Please try again.');
    }
    // The AuthGuard will handle navigation automatically.
    setSigningOut(false);
  };

  const handleResetSettings = () => {
    Alert.alert(
      'Reset Settings',
      'Are you sure you want to reset all settings to default?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: () => {
            const defaultSettings: PomodoroSettings = {
              workDuration: 25,
              shortBreakDuration: 5,
              longBreakDuration: 15,
              sessionsUntilLongBreak: 4,
            };
            setLocalSettings(defaultSettings);
            savePomodoroSettings(defaultSettings);
          },
        },
      ]
    );
  };

  const handleClearData = () => {
    Alert.alert(
      'Clear All Data',
      'This will permanently delete all your timer sessions, subjects, and goals. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear Data',
          style: 'destructive',
          onPress: async () => {
            try {
              // Clear all data using storage service
              const { storageService } = require('@/services/storageService');
              await storageService.multiRemove([
                'isotope_timer_sessions',
                'isotope_subjects',
                'isotope_goals',
                'isotope_pomodoro_settings',
                'distraction_blocking_data'
              ]);
              Alert.alert('Success', 'All data has been cleared.');
            } catch (error) {
              Alert.alert('Error', 'Failed to clear data.');
            }
          },
        },
      ]
    );
  };

  const exportData = () => {
    if (typeof window !== 'undefined' && window.localStorage) {
      const data = {
        sessions: localStorage.getItem('isotope_timer_sessions'),
        subjects: localStorage.getItem('isotope_subjects'),
        goals: localStorage.getItem('isotope_goals'),
        settings: localStorage.getItem('isotope_pomodoro_settings'),
        exportDate: new Date().toISOString(),
      };
      
      const dataStr = JSON.stringify(data, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `isotope-backup-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      Alert.alert('Success', 'Data exported successfully!');
    }
  };

  const settingsStyles = styles(theme);

  return (
    <ScrollView style={settingsStyles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <LinearGradient
        colors={themeMode === 'dark' ? ['#1E1E1E', '#2A2A2A', '#1E1E1E'] : ['#F8FAFC', '#EEF2FF', '#F8FAFC']}
        style={settingsStyles.headerGradient}
      >
        <View style={settingsStyles.header}>
          <View>
            <IsotopeLogo size="medium" />
            <Text style={settingsStyles.subtitle}>Customize your experience</Text>
          </View>
        </View>
      </LinearGradient>

      {/* Account Section */}
      <View style={settingsStyles.section}>
        <View style={settingsStyles.sectionHeader}>
          <User size={20} color={theme.colors.accent.primary} />
          <Text style={settingsStyles.sectionTitle}>Account</Text>
        </View>

        <View style={settingsStyles.settingsCard}>
          <View style={settingsStyles.accountInfo}>
            <View style={settingsStyles.userAvatar}>
              <Text style={settingsStyles.userInitial}>
                {user?.email?.charAt(0).toUpperCase() || 'U'}
              </Text>
            </View>
            <View style={settingsStyles.userDetails}>
              <Text style={settingsStyles.userEmail}>{user?.email}</Text>
              <View style={settingsStyles.syncStatus}>
                <Cloud size={16} color={theme.colors.status.success} />
                <Text style={settingsStyles.syncText}>Synced to cloud</Text>
              </View>
            </View>
          </View>

          <TouchableOpacity
            style={[settingsStyles.signOutButton, signingOut && settingsStyles.signOutButtonDisabled]}
            onPress={() => {
              Alert.alert(
                'Sign Out',
                'Are you sure you want to sign out?',
                [
                  { text: 'Cancel', style: 'cancel' },
                  {
                    text: 'Sign Out',
                    style: 'destructive',
                    onPress: handleSignOut,
                  },
                ]
              );
            }}
            disabled={signingOut}
          >
            <LogOut size={20} color={signingOut ? theme.colors.text.disabled : theme.colors.status.error} />
            <Text style={[settingsStyles.signOutText, signingOut && settingsStyles.signOutTextDisabled]}>
              {signingOut ? 'Signing Out...' : 'Sign Out'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Pomodoro Settings */}
      <View style={settingsStyles.section}>
        <View style={settingsStyles.sectionHeader}>
          <Timer size={20} color={theme.colors.accent.primary} />
          <Text style={settingsStyles.sectionTitle}>Pomodoro Timer</Text>
        </View>

        <View style={settingsStyles.settingsCard}>
          <View style={settingsStyles.settingItem}>
            <Text style={settingsStyles.settingLabel}>Work Duration (minutes)</Text>
            <TextInput
              style={settingsStyles.settingInput}
              value={localSettings.workDuration.toString()}
              onChangeText={(text) =>
                setLocalSettings(prev => ({ ...prev, workDuration: parseInt(text) || 25 }))
              }
              keyboardType="number-pad"
              placeholder="25"
              placeholderTextColor={theme.colors.interactive.input.placeholder}
            />
          </View>

          <View style={settingsStyles.settingItem}>
            <Text style={settingsStyles.settingLabel}>Short Break (minutes)</Text>
            <TextInput
              style={settingsStyles.settingInput}
              value={localSettings.shortBreakDuration.toString()}
              onChangeText={(text) => 
                setLocalSettings(prev => ({ ...prev, shortBreakDuration: parseInt(text) || 5 }))
              }
              keyboardType="number-pad"
              placeholder="5"
            />
          </View>
          
          <View style={settingsStyles.settingItem}>
            <Text style={settingsStyles.settingLabel}>Long Break (minutes)</Text>
            <TextInput
              style={settingsStyles.settingInput}
              value={localSettings.longBreakDuration.toString()}
              onChangeText={(text) =>
                setLocalSettings(prev => ({ ...prev, longBreakDuration: parseInt(text) || 15 }))
              }
              keyboardType="number-pad"
              placeholder="15"
              placeholderTextColor={theme.colors.interactive.input.placeholder}
            />
          </View>

          <View style={settingsStyles.settingItem}>
            <Text style={settingsStyles.settingLabel}>Sessions until Long Break</Text>
            <TextInput
              style={settingsStyles.settingInput}
              value={localSettings.sessionsUntilLongBreak.toString()}
              onChangeText={(text) =>
                setLocalSettings(prev => ({ ...prev, sessionsUntilLongBreak: parseInt(text) || 4 }))
              }
              keyboardType="number-pad"
              placeholder="4"
              placeholderTextColor={theme.colors.interactive.input.placeholder}
            />
          </View>

          <TouchableOpacity style={settingsStyles.saveButton} onPress={handleSaveSettings}>
            <LinearGradient
              colors={themeMode === 'dark' ? [theme.colors.accent.primary, theme.colors.accent.secondary] : ['#6366F1', '#8B5CF6']}
              style={settingsStyles.saveGradient}
            >
              <Text style={settingsStyles.saveText}>Save Settings</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </View>

      {/* Notifications */}
      <View style={settingsStyles.section}>
        <View style={settingsStyles.sectionHeader}>
          <Bell size={20} color={theme.colors.status.success} />
          <Text style={settingsStyles.sectionTitle}>Notifications</Text>
        </View>

        <View style={settingsStyles.settingsCard}>
          <Switch
            value={notificationsEnabled}
            onValueChange={setNotificationsEnabled}
            label="Enable Notifications"
            description="Get notified when timer sessions complete"
            size="md"
            style={settingsStyles.switchItem}
          />

          <Switch
            value={soundEnabled}
            onValueChange={setSoundEnabled}
            label="Sound Alerts"
            description="Play sound when timer completes"
            size="md"
            style={settingsStyles.switchItem}
          />
        </View>
      </View>

      {/* Appearance */}
      <View style={settingsStyles.section}>
        <View style={settingsStyles.sectionHeader}>
          <Palette size={20} color={theme.colors.accent.secondary} />
          <Text style={settingsStyles.sectionTitle}>Appearance</Text>
        </View>

        <View style={settingsStyles.settingsCard}>
          <Switch
            value={themeMode === 'dark'}
            onValueChange={toggleTheme}
            label="Dark Mode"
            description="Switch between light and dark themes"
            size="md"
            style={settingsStyles.switchItem}
          />
        </View>
      </View>

      {/* Data Management */}
      <View style={settingsStyles.section}>
        <View style={settingsStyles.sectionHeader}>
          <SettingsIcon size={20} color={theme.colors.status.warning} />
          <Text style={settingsStyles.sectionTitle}>Data Management</Text>
        </View>

        <View style={settingsStyles.settingsCard}>
          <TouchableOpacity style={settingsStyles.actionButton} onPress={exportData}>
            <Download size={20} color={theme.colors.accent.primary} />
            <View style={settingsStyles.actionInfo}>
              <Text style={settingsStyles.actionLabel}>Export Data</Text>
              <Text style={settingsStyles.actionDescription}>
                Download your data as a backup file
              </Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={settingsStyles.actionButton} onPress={handleResetSettings}>
            <Upload size={20} color={theme.colors.status.warning} />
            <View style={settingsStyles.actionInfo}>
              <Text style={settingsStyles.actionLabel}>Reset Settings</Text>
              <Text style={settingsStyles.actionDescription}>
                Restore default timer settings
              </Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity style={settingsStyles.actionButton} onPress={handleClearData}>
            <Trash2 size={20} color={theme.colors.status.error} />
            <View style={settingsStyles.actionInfo}>
              <Text style={settingsStyles.actionLabel}>Clear All Data</Text>
              <Text style={settingsStyles.actionDescription}>
                Permanently delete all your data
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>

      {/* About */}
      <View style={settingsStyles.section}>
        <View style={settingsStyles.sectionHeader}>
          <Info size={20} color={theme.colors.accent.secondary} />
          <Text style={settingsStyles.sectionTitle}>About</Text>
        </View>

        <View style={settingsStyles.settingsCard}>
          <View style={settingsStyles.aboutContent}>
            <IsotopeLogo size="large" />
            <Text style={settingsStyles.appName}>IsotopeAI Time Management</Text>
            <Text style={settingsStyles.version}>Version 1.0.0</Text>
            <Text style={settingsStyles.description}>
              A powerful time management application designed to help you maximize
              your productivity with advanced timer features, goal tracking, and
              detailed analytics.
            </Text>
          </View>
        </View>
      </View>

      {/* Tips */}
      <View style={settingsStyles.section}>
        <Text style={settingsStyles.sectionTitle}>💡 Tips</Text>

        <View style={settingsStyles.tipCard}>
          <Text style={settingsStyles.tipTitle}>Pomodoro Technique</Text>
          <Text style={settingsStyles.tipText}>
            The traditional Pomodoro technique uses 25-minute work sessions with 5-minute breaks.
            After 4 sessions, take a longer 15-30 minute break.
          </Text>
        </View>

        <View style={settingsStyles.tipCard}>
          <Text style={settingsStyles.tipTitle}>Customize Your Settings</Text>
          <Text style={settingsStyles.tipText}>
            Adjust timer durations based on your attention span and work style.
            Some people work better with longer or shorter intervals.
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background.primary,
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 20,
  },
  header: {
    paddingHorizontal: 24,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text.secondary,
    marginTop: 4,
  },
  section: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text.primary,
  },
  settingsCard: {
    backgroundColor: theme.colors.background.card,
    borderRadius: 16,
    padding: 20,
    shadowColor: theme.colors.ui.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  settingItem: {
    marginBottom: 16,
  },
  settingLabel: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text.primary,
    marginBottom: 8,
  },
  settingDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text.secondary,
    marginTop: 2,
  },
  settingInput: {
    backgroundColor: theme.colors.interactive.input.background,
    borderWidth: 1,
    borderColor: theme.colors.interactive.input.border,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text.primary,
  },
  switchItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  switchInfo: {
    flex: 1,
    marginRight: 16,
  },
  saveButton: {
    borderRadius: 12,
    overflow: 'hidden',
    marginTop: 8,
  },
  saveGradient: {
    paddingVertical: 14,
    alignItems: 'center',
  },
  saveText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    gap: 12,
  },
  actionInfo: {
    flex: 1,
  },
  actionLabel: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: theme.colors.text.primary,
  },
  actionDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text.secondary,
    marginTop: 2,
  },
  aboutContent: {
    alignItems: 'center',
    gap: 12,
  },
  appName: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: theme.colors.text.primary,
    textAlign: 'center',
  },
  version: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text.secondary,
  },
  description: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text.secondary,
    textAlign: 'center',
    lineHeight: 20,
    marginTop: 8,
  },
  tipCard: {
    backgroundColor: theme.colors.background.card,
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: theme.colors.ui.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tipTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text.primary,
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.text.secondary,
    lineHeight: 20,
  },
  // Account styles
  accountInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.ui.border,
  },
  userAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.accent.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  userInitial: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  userDetails: {
    flex: 1,
  },
  userEmail: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.text.primary,
    marginBottom: 4,
  },
  syncStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  syncText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: theme.colors.status.success,
  },
  signOutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.status.error + '15',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: theme.colors.status.error + '30',
    gap: 8,
  },
  signOutText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: theme.colors.status.error,
  },
  signOutButtonDisabled: {
    backgroundColor: theme.colors.background.secondary,
    borderColor: theme.colors.ui.border,
  },
  signOutTextDisabled: {
    color: theme.colors.text.disabled,
  },
});
