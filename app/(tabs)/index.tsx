import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Dimensions,
  Alert,
  TouchableOpacity,
} from 'react-native';

import { LinearGradient } from 'expo-linear-gradient';
import {
  Play,
  Pause,
  Square,
  RotateCcw,
  Clock,
  Timer as TimerIcon,
  Settings as SettingsIcon,
  Shield,
  Volume2,
  Smartphone,
  Zap,
  Target,
} from 'lucide-react-native';

import IsotopeLogo from '@/components/IsotopeLogo';
import SubjectPicker from '@/components/SubjectPicker';
import { IconButton, Card } from '@/components/ui';
import { useTimer } from '@/hooks/useTimer';
import { useDistractionBlocking } from '@/hooks/useDistractionBlocking';
import { useTheme } from '@/contexts/ThemeContext';
import { useRouter } from 'expo-router';
import BlockingNotifications, { NotificationData } from '@/components/BlockingNotifications';
import DistractionBlockingDemo from '@/components/DistractionBlockingDemo';
import { notificationService } from '@/services/notificationService';

const { width } = Dimensions.get('window');

interface SoundOption {
  id: string;
  name: string;
  icon: string;
  active: boolean;
}

export default function TimerScreen() {
  const router = useRouter();
  const { theme } = useTheme();
  const {
    isRunning,
    time,
    mode,
    currentSubject,
    pomodoroPhase,
    pomodoroSession,
    startTimer,
    pauseTimer,
    stopTimer,
    resetTimer,
    switchMode,
    setCurrentSubject,
    formatTime,
    getTotalTimeToday,
  } = useTimer();

  // Use distraction blocking hook
  const {
    isBlockingEnabled,
    isActivelyBlocking,
    currentBlockingReason,
    stats,
    recentlyBlockedApps,
    setFocusContext,
    recordDistractionAttempt,
    startTimerBlocking,
    stopTimerBlocking,
    toggleManualBlocking,
    isLoading: isBlockingLoading,
  } = useDistractionBlocking();

  // Local state
  const [showSettings, setShowSettings] = useState(false);
  const [showDemo, setShowDemo] = useState(false);
  const [notifications, setNotifications] = useState<NotificationData[]>([]);

  // Focus sounds state
  const [soundOptions, setSoundOptions] = useState<SoundOption[]>([
    { id: '1', name: 'Rain', icon: '🌧️', active: false },
    { id: '2', name: 'Forest', icon: '🌲', active: false },
    { id: '3', name: 'Ocean', icon: '🌊', active: false },
    { id: '4', name: 'Coffee Shop', icon: '☕', active: false },
  ]);

  // Update distraction blocking context when timer state changes
  useEffect(() => {
    if (isRunning) {
      // Start timer-based blocking
      startTimerBlocking();

      // Set appropriate focus context
      if (mode === 'pomodoro') {
        setFocusContext(pomodoroPhase === 'work' ? 'focus' : 'break');
      } else {
        // For stopwatch, always use focus context
        setFocusContext('focus');
      }
    } else {
      // Stop timer-based blocking
      stopTimerBlocking();
      setFocusContext('normal');
    }
  }, [isRunning, mode, pomodoroPhase, setFocusContext, startTimerBlocking, stopTimerBlocking]);

  // Listen for distraction attempts to show in-app notifications
  useEffect(() => {
    if (!isRunning) return;

    const { mobileAppMonitoringService } = require('@/services/mobileAppMonitoringService');

    const unsubscribe = mobileAppMonitoringService.onDistractionAttempt((attempt: any) => {
      if (attempt.blocked) {
        addNotification({
          type: 'warning',
          title: '🚫 App Blocked',
          message: `${attempt.appName} is blocked during focus time. Stay focused!`,
          duration: 4000,
        });
      }
    });

    return unsubscribe;
  }, [isRunning]);

  const addNotification = (notification: Omit<NotificationData, 'id'>) => {
    const newNotification: NotificationData = {
      ...notification,
      id: Date.now().toString(),
    };
    setNotifications(prev => [...prev, newNotification]);
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const toggleSound = (soundId: string) => {
    setSoundOptions(prev =>
      prev.map(sound =>
        sound.id === soundId
          ? { ...sound, active: !sound.active }
          : { ...sound, active: false }
      )
    );
  };

  const handleModeSwitch = (newMode: 'stopwatch' | 'pomodoro') => {
    if (isRunning) {
      stopTimer();
    }
    switchMode(newMode);
  };

  const handleStartTimer = () => {
    if (!currentSubject) {
      Alert.alert(
        'No Subject Selected',
        'Please select a subject before starting the timer.'
      );
      return;
    }

    startTimer();

    // Show focus notification when timer starts (automatic focus mode)
    addNotification({
      type: 'info',
      title: '🎯 Focus Mode Activated',
      message: 'Timer started! Distracting apps are now blocked.',
      duration: 3000,
    });
  };

  const getTimerProgress = () => {
    if (mode === 'stopwatch') return 0;

    const totalTime = pomodoroPhase === 'work' ? 25 * 60 : 5 * 60; // Simplified for demo
    return ((totalTime - time) / totalTime) * 100;
  };

  const getPhaseText = () => {
    if (mode === 'stopwatch') {
      return isRunning ? 'Focus Time' : 'Stopwatch';
    }
    return pomodoroPhase === 'work' ? 'Focus Time' : 'Break Time';
  };

  const getPhaseColor = (): [string, string] => {
    if (mode === 'stopwatch') {
      return isRunning ? ['#6B46C1', '#7C3AED'] : ['#6366F1', '#8B5CF6'];
    }
    return pomodoroPhase === 'work' ? ['#6B46C1', '#7C3AED'] : ['#10B981', '#34D399'];
  };

  const getPhaseIcon = () => {
    if (mode === 'stopwatch') {
      return isRunning ? '🎯' : '⏱️';
    }
    return pomodoroPhase === 'work' ? '🎯' : '☕';
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background.primary }]}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        style={{ backgroundColor: theme.colors.background.primary }}
      >
        {/* Header */}
        <View style={[styles.headerGradient, { backgroundColor: theme.colors.background.primary }]}>
          <View style={styles.header}>
            <IsotopeLogo size="medium" />
            <View style={styles.headerActions}>
              {isRunning && (
                <TouchableOpacity
                  style={[styles.demoButton, { backgroundColor: theme.colors.background.secondary }]}
                  onPress={() => setShowDemo(!showDemo)}
                >
                  <Text style={[styles.demoButtonText, { color: theme.colors.text.primary }]}>
                    {showDemo ? 'Hide Demo' : 'Test Blocking'}
                  </Text>
                </TouchableOpacity>
              )}
              <TouchableOpacity
                style={[styles.settingsButton, { backgroundColor: theme.colors.background.secondary, ...theme.shadows.md }]}
                onPress={() => setShowSettings(!showSettings)}
              >
                <SettingsIcon size={20} color={theme.colors.text.secondary} />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Mode Selector */}
        <View style={styles.modeSection}>
          <View style={[styles.modeSelector, { backgroundColor: theme.colors.background.card, ...theme.shadows.md }]}>
            <TouchableOpacity
              style={[
                styles.modeButton,
                mode === 'stopwatch' && { backgroundColor: theme.colors.accent.primary },
              ]}
              onPress={() => handleModeSwitch('stopwatch')}
            >
              <Clock size={20} color={mode === 'stopwatch' ? theme.colors.text.inverse : theme.colors.text.secondary} />
              <Text
                style={[
                  styles.modeText,
                  { color: mode === 'stopwatch' ? theme.colors.text.inverse : theme.colors.text.secondary },
                ]}
              >
                Stopwatch
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.modeButton,
                mode === 'pomodoro' && { backgroundColor: theme.colors.accent.primary },
              ]}
              onPress={() => handleModeSwitch('pomodoro')}
            >
              <TimerIcon size={20} color={mode === 'pomodoro' ? theme.colors.text.inverse : theme.colors.text.secondary} />
              <Text
                style={[
                  styles.modeText,
                  { color: mode === 'pomodoro' ? theme.colors.text.inverse : theme.colors.text.secondary },
                ]}
              >
                Pomodoro
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Subject Picker */}
        <View style={styles.subjectSection}>
          <SubjectPicker
            selectedSubject={currentSubject}
            onSelectSubject={setCurrentSubject}
          />
        </View>

        {/* Focus Stats - Only show when timer is running */}
        {isRunning && (
          <View style={styles.statsSection}>
            <View style={styles.statsGrid}>
              <View style={[styles.statCard, { backgroundColor: theme.colors.background.card, ...theme.shadows.md }]}>
                <Zap size={20} color={theme.colors.accent.primary} />
                <Text style={[styles.statValue, { color: theme.colors.text.primary }]}>
                  {mode === 'pomodoro' ? pomodoroSession : '1'}
                </Text>
                <Text style={[styles.statLabel, { color: theme.colors.text.secondary }]}>Session</Text>
              </View>

              <View style={[styles.statCard, { backgroundColor: theme.colors.background.card, ...theme.shadows.md }]}>
                <Target size={20} color={theme.colors.status.success} />
                <Text style={[styles.statValue, { color: theme.colors.text.primary }]}>
                  {mode === 'pomodoro' ? '4' : '∞'}
                </Text>
                <Text style={[styles.statLabel, { color: theme.colors.text.secondary }]}>Goal</Text>
              </View>

              <View style={[styles.statCard, { backgroundColor: theme.colors.background.card, ...theme.shadows.md }]}>
                <Smartphone size={20} color={theme.colors.status.error} />
                <Text style={[styles.statValue, { color: theme.colors.text.primary }]}>
                  {stats.blockedToday}
                </Text>
                <Text style={[styles.statLabel, { color: theme.colors.text.secondary }]}>Blocked</Text>
              </View>
            </View>
          </View>
        )}

        {/* Main Timer */}
        <View style={styles.timerSection}>
          <View style={styles.timerContainer}>
            <LinearGradient
              colors={theme.mode === 'dark'
                ? [`${theme.colors.background.card}33`, `${theme.colors.background.card}1A`]
                : ['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']
              }
              style={[
                styles.timerCard,
                {
                  backgroundColor: theme.colors.background.card,
                  borderColor: theme.colors.ui.border,
                  ...theme.shadows.lg,
                },
              ]}
            >
              <View style={styles.timerHeader}>
                <View style={styles.phaseIndicator}>
                  <LinearGradient
                    colors={getPhaseColor()}
                    style={styles.phaseGradient}
                  >
                    <Text style={styles.phaseIcon}>
                      {getPhaseIcon()}
                    </Text>
                  </LinearGradient>
                </View>
                <View style={styles.timerInfo}>
                  <Text style={[styles.timerLabel, { color: theme.colors.text.primary }]}>{getPhaseText()}</Text>
                  {mode === 'pomodoro' && (
                    <Text style={[styles.cycleText, { color: theme.colors.text.secondary }]}>
                      Session {pomodoroSession}
                    </Text>
                  )}
                  {(isRunning || isActivelyBlocking) && (
                    <Text style={[styles.focusIndicator, { color: theme.colors.status.success }]}>
                      🛡️ {isActivelyBlocking ?
                        `${currentBlockingReason === 'timer' ? 'Timer' : 'Manual'} blocking active` :
                        'Focus mode active'}
                    </Text>
                  )}
                </View>
              </View>

              <View style={styles.timerDisplay}>
                <Text style={[styles.timerText, { color: theme.colors.text.primary }]}>{formatTime(time)}</Text>

                {mode === 'pomodoro' && (
                  <View style={styles.progressContainer}>
                    <View style={[styles.progressTrack, { backgroundColor: theme.colors.ui.border }]}>
                      <LinearGradient
                        colors={getPhaseColor()}
                        style={[
                          styles.progressFill,
                          { width: `${getTimerProgress()}%` }
                        ]}
                      />
                    </View>
                    <Text style={[styles.progressText, { color: theme.colors.text.secondary }]}>
                      {Math.round(getTimerProgress())}% Complete
                    </Text>
                  </View>
                )}
              </View>
            </LinearGradient>
          </View>

          {/* Timer Controls */}
          <View style={styles.timerControls}>
            <IconButton
              icon={<RotateCcw size={24} color={theme.colors.accent.primary} />}
              onPress={resetTimer}
              variant="secondary"
              size="lg"
            />

            <IconButton
              icon={isRunning ? <Pause size={32} color="#FFFFFF" /> : <Play size={32} color="#FFFFFF" />}
              onPress={isRunning ? pauseTimer : handleStartTimer}
              variant="primary"
              size="xl"
              gradient
            />

            <IconButton
              icon={<Square size={24} color={theme.colors.status.error} />}
              onPress={stopTimer}
              variant="secondary"
              size="lg"
            />
          </View>
        </View>

        {/* Focus Sounds - Show when timer is running */}
        {isRunning && (
          <View style={styles.soundsSection}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>Focus Sounds</Text>
            <View style={styles.soundGrid}>
              {soundOptions.map((sound) => (
                <Card
                  key={sound.id}
                  variant={sound.active ? "elevated" : "default"}
                  size="sm"
                  onPress={() => toggleSound(sound.id)}
                  style={[
                    styles.soundCard,
                    sound.active && {
                      backgroundColor: theme.colors.accent.primary,
                    },
                  ]}
                >
                  <Text style={styles.soundIcon}>{sound.icon}</Text>
                  <Text
                    style={[
                      styles.soundName,
                      { color: sound.active ? theme.colors.text.inverse : theme.colors.text.primary },
                    ]}
                  >
                    {sound.name}
                  </Text>
                  {sound.active && (
                    <View style={[styles.activeBadge, { backgroundColor: theme.colors.accent.primary }]}>
                      <Volume2 size={16} color="#FFFFFF" />
                    </View>
                  )}
                </Card>
              ))}
            </View>
          </View>
        )}

        {/* Distraction Blocking - Always show for settings access */}
        <View style={styles.blockingSection}>
          <View style={styles.blockingSectionHeader}>
            <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>Distraction Blocking</Text>
            <TouchableOpacity
              style={[styles.settingsButton, { backgroundColor: theme.colors.background.secondary }]}
              onPress={() => router.push('/distraction-settings')}
            >
              <SettingsIcon size={20} color={theme.colors.text.secondary} />
            </TouchableOpacity>
          </View>

          <View style={[styles.blockingCard, { backgroundColor: theme.colors.background.card, shadowColor: theme.shadows.md.shadowColor, shadowOpacity: theme.shadows.md.shadowOpacity, shadowOffset: theme.shadows.md.shadowOffset, shadowRadius: theme.shadows.md.shadowRadius, elevation: theme.shadows.md.elevation }]}>
            <View style={styles.blockingHeader}>
              <Shield size={24} color={isActivelyBlocking ? theme.colors.status.success : theme.colors.text.secondary} />
              <Text style={[styles.blockingTitle, { color: theme.colors.text.primary }]}>
                {isActivelyBlocking ?
                  `${currentBlockingReason === 'timer' ? 'Timer' : 'Manual'} Blocking` :
                  'Distraction Blocking'}
              </Text>
              <View style={[
                styles.statusBadge,
                { backgroundColor: isActivelyBlocking ? theme.colors.status.success : theme.colors.text.secondary }
              ]}>
                <Text style={[styles.statusText, { color: theme.colors.text.inverse }]}>
                  {isActivelyBlocking ? "ACTIVE" : "READY"}
                </Text>
              </View>
            </View>

            {/* Manual blocking toggle - only show when not timer-based */}
            {!isRunning && (
              <TouchableOpacity
                style={[
                  styles.manualBlockingButton,
                  { backgroundColor: theme.colors.background.secondary, borderColor: theme.colors.accent.primary },
                  isActivelyBlocking && { backgroundColor: theme.colors.accent.primary, borderColor: theme.colors.accent.primary }
                ]}
                onPress={toggleManualBlocking}
              >
                <Shield size={20} color={isActivelyBlocking ? theme.colors.text.inverse : theme.colors.accent.primary} />
                <Text style={[
                  styles.manualBlockingButtonText,
                  { color: isActivelyBlocking ? theme.colors.text.inverse : theme.colors.accent.primary }
                ]}>
                  {isActivelyBlocking ? 'Stop Manual Blocking' : 'Start Manual Blocking'}
                </Text>
              </TouchableOpacity>
            )}

            <Text style={[styles.blockingDescription, { color: theme.colors.text.secondary }]}>
              {isActivelyBlocking
                ? `Distracting apps are currently blocked. ${currentBlockingReason === 'timer' ? 'Timer-based' : 'Manual'} blocking is active!`
                : isBlockingEnabled
                ? "Distraction blocking will automatically activate when you start any timer, or you can start it manually."
                : "Enable distraction blocking in settings to automatically block distracting apps during timer sessions."
              }
            </Text>

            {isRunning && recentlyBlockedApps.length > 0 && (
              <View style={[styles.recentlyBlocked, { borderTopColor: theme.colors.ui.border }]}>
                <Text style={[styles.recentlyBlockedTitle, { color: theme.colors.text.primary }]}>Recently Blocked</Text>
                <View style={styles.recentlyBlockedList}>
                  {recentlyBlockedApps.slice(0, 3).map((app, index) => (
                    <View key={index} style={styles.blockedAppItem}>
                      <Text style={[styles.blockedAppName, { color: theme.colors.text.primary }]}>{app.name}</Text>
                      <Text style={[styles.blockedAppTime, { color: theme.colors.text.secondary }]}>
                        {new Date(app.lastAttempt).toLocaleTimeString([], {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </Text>
                    </View>
                  ))}
                </View>
              </View>
            )}
          </View>
        </View>

        {/* Today's Stats */}
        <View style={styles.progressSection}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>Today's Progress</Text>

          <View style={[styles.progressCard, { backgroundColor: theme.colors.background.card, shadowColor: theme.shadows.md.shadowColor, shadowOpacity: theme.shadows.md.shadowOpacity, shadowOffset: theme.shadows.md.shadowOffset, shadowRadius: theme.shadows.md.shadowRadius, elevation: theme.shadows.md.elevation }]}>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: theme.colors.text.primary }]}>{formatTime(getTotalTimeToday())}</Text>
              <Text style={[styles.statLabel, { color: theme.colors.text.secondary }]}>Total Time</Text>
            </View>

            <View style={[styles.statDivider, { backgroundColor: theme.colors.ui.border }]} />

            <View style={styles.statItem}>
              <Text style={[styles.statValue, { textAlign: 'center', color: theme.colors.text.primary }]}>
                {currentSubject ? currentSubject.name : 'No Subject'}
              </Text>
              <Text style={[styles.statLabel, { color: theme.colors.text.secondary }]}>Current Subject</Text>
            </View>
          </View>
        </View>

        {/* Quick Tips */}
        <View style={styles.tipsSection}>
          <Text style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>Tips</Text>

          <View style={[styles.tipCard, { backgroundColor: theme.colors.background.card, shadowColor: theme.shadows.md.shadowColor, shadowOpacity: theme.shadows.md.shadowOpacity, shadowOffset: theme.shadows.md.shadowOffset, shadowRadius: theme.shadows.md.shadowRadius, elevation: theme.shadows.md.elevation }]}>
            <Text style={[styles.tipTitle, { color: theme.colors.text.primary }]}>
              {mode === 'stopwatch' ? '⏱️ Stopwatch Mode' : '🍅 Pomodoro Technique'}
            </Text>
            <Text style={[styles.tipText, { color: theme.colors.text.secondary }]}>
              {mode === 'stopwatch'
                ? 'Perfect for open-ended study sessions. Starting the timer automatically enables focus mode to block distractions.'
                : 'Work for 25 minutes, then take a 5-minute break. Focus mode automatically activates during work sessions to block distracting apps.'
              }
            </Text>
          </View>
        </View>

        {/* Demo Section */}
        {isRunning && isBlockingEnabled && (
          <DistractionBlockingDemo isVisible={showDemo} />
        )}

      </ScrollView>

      {/* Notifications */}
      <BlockingNotifications
        notifications={notifications}
        onDismiss={removeNotification}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 20,
    // The background color is applied inline in the component
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  demoButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  demoButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
  },
  settingsButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modeSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  modeSelector: {
    flexDirection: 'row',
    borderRadius: 16,
    padding: 4,
  },
  modeButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderRadius: 16,
    gap: 8,
  },
  modeButtonActive: {
    backgroundColor: '#6B46C1', // This will be overridden by theme.colors.accent.primary where used
  },
  modeText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  modeTextActive: {
    color: '#FFFFFF', // This will be overridden by theme.colors.text.inverse where used
  },
  subjectSection: {
    paddingHorizontal: 24,
  },
  statsSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  statCard: {
    flex: 1,
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    gap: 8,
    // Shadows are applied inline where statCard is used
  },
  statValue: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    textAlign: 'center',
  },
  timerSection: {
    alignItems: 'center',
    marginVertical: 32,
    paddingHorizontal: 20,
  },
  timerContainer: {
    marginBottom: 32,
    width: '100%',
  },
  timerCard: {
    borderRadius: 32,
    padding: 32,
    borderWidth: 1,
  },
  timerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    gap: 16,
  },
  phaseIndicator: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  phaseGradient: {
    width: 48,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },
  phaseIcon: {
    fontSize: 24,
  },
  timerInfo: {
    flex: 1,
  },
  timerLabel: {
    fontSize: 18,
    fontFamily: 'Inter-Semibold',
  },
  cycleText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    marginTop: 2,
  },
  focusIndicator: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    marginTop: 4,
  },
  timerDisplay: {
    alignItems: 'center',
  },
  timerText: {
    fontSize: 64,
    fontFamily: 'Inter-Bold',
    textAlign: 'center',
    marginBottom: 24,
    letterSpacing: -2,
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
    gap: 12,
  },
  progressTrack: {
    width: '100%',
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
  },
  timerControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 20,
    marginTop: 8,
  },
  controlButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    // Shadows are applied inline where controlButton is used
  },
  playButton: {
    width: 80,
    height: 80,
    borderRadius: 24,
  },
  playGradient: {
    width: '100%',
    height: '100%',
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  soundsSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  soundGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  soundCard: {
    width: (width - 72) / 2,
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
    // Shadows are applied inline where soundCard is used
  },
  soundCardActive: {
    borderColor: '#6B46C1', // This will be overridden by theme.colors.accent.primary where used
    backgroundColor: '#F3F4F6', // This will be overridden by theme.colors.background.tertiary where used
  },
  soundIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  soundName: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    textAlign: 'center',
  },
  soundNameActive: {
    color: '#6B46C1', // This will be overridden by theme.colors.text.inverse where used
  },
  activeBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#6B46C1', // This will be overridden by theme.colors.accent.primary where used
    borderRadius: 12,
    width: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  blockingSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  blockingSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  blockingCard: {
    borderRadius: 16,
    padding: 20,
    // Shadows are applied inline where blockingCard is used
  },
  blockingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 12,
  },
  blockingTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Semibold',
    flex: 1,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  blockingDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
    marginBottom: 16,
  },
  recentlyBlocked: {
    borderTopWidth: 1,
    paddingTop: 16,
    // Border color applied inline
  },
  recentlyBlockedTitle: {
    fontSize: 14,
    fontFamily: 'Inter-Semibold',
    marginBottom: 8,
  },
  recentlyBlockedList: {
    gap: 8,
  },
  blockedAppItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  blockedAppName: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    // Color applied inline
  },
  blockedAppTime: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    // Color applied inline
  },
  manualBlockingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginTop: 12,
    borderWidth: 1,
    // Background and border colors applied inline
  },
  manualBlockingButtonActive: {
    // These are now handled by inline styles
  },
  manualBlockingButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    marginLeft: 8,
    // Color applied inline
  },
  manualBlockingButtonTextActive: {
    // This is now handled by inline styles
  },
  progressSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 16,
    // Color applied inline
  },
  progressCard: {
    borderRadius: 16,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    // Shadows are applied inline where progressCard is used
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statDivider: {
    width: 1,
    height: 40,
    marginHorizontal: 20,
  },
  tipsSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
    marginBottom: 32,
  },
  tipCard: {
    borderRadius: 16,
    padding: 20,
    // Shadows are applied inline where tipCard is used
  },
  tipTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    marginBottom: 8,
    // Color applied inline
  },
  tipText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    lineHeight: 20,
    // Color applied inline
  },

});
